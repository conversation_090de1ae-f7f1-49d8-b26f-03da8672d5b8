# SSL证书扫描器

一个用于扫描SSL证书并查找使用相同证书的其他域名的Java图形化应用程序。

## 功能特性

- 🔍 **单域名扫描**: 输入单个域名，获取其SSL证书信息
- 📋 **批量扫描**: 支持批量扫描多个域名
- 📁 **文件导入**: 从文本文件导入域名列表
- 🔗 **相关域名发现**: 自动查找使用相同证书的其他域名
- 📊 **结果展示**: 表格形式展示扫描结果，支持排序和筛选
- 💾 **结果导出**: 将扫描结果导出为CSV格式
- 🖥️ **图形界面**: 用户友好的Swing GUI界面

## 系统要求

- Java 8 或更高版本
- 网络连接（用于SSL证书获取和API查询）

## 安装和运行

### 方式一：直接运行JAR文件

1. 下载 `certificate-scanner-1.0.0.jar` 文件
2. 在命令行中运行：
   ```bash
   java -jar certificate-scanner-1.0.0.jar
   ```

### 方式二：从源码构建

1. 确保已安装Java 8+和Maven
2. 克隆或下载源代码
3. 在项目根目录执行：
   ```bash
   mvn clean package -DskipTests
   ```
4. 运行生成的JAR文件：
   ```bash
   java -jar target/certificate-scanner-1.0.0.jar
   ```

## 使用说明

### 单域名扫描

1. 在"域名"输入框中输入要扫描的域名（如：www.google.com）
2. 点击"扫描单个域名"按钮或按回车键
3. 等待扫描完成，结果将显示在下方的表格中

### 批量扫描

1. 在"批量域名"文本区域中输入多个域名，每行一个
2. 或者点击"从文件加载"按钮导入域名列表文件
3. 点击"批量扫描"按钮
4. 观察进度条，等待所有域名扫描完成

### 查看详细信息

- 双击结果表格中的任意行，可以查看该域名的详细证书信息

### 导出结果

- 点击"导出结果"按钮，将当前扫描结果保存为CSV文件

## 支持的域名格式

- 标准域名：`example.com`
- 子域名：`www.example.com`
- 支持通配符证书的域名发现

## 数据来源

本工具使用以下数据源来查找相关域名：

- **crt.sh**: 免费的证书透明度日志搜索服务
- **直接SSL连接**: 直接连接目标域名获取证书信息

## 注意事项

1. **网络要求**: 需要稳定的网络连接来获取证书信息
2. **扫描速度**: 批量扫描时，每个域名的扫描可能需要几秒钟时间
3. **证书有效性**: 工具会显示证书的有效期状态（正常/即将过期/已过期）
4. **API限制**: crt.sh等服务可能有访问频率限制，大量扫描时请适当控制频率

## 故障排除

### 常见问题

1. **无法启动应用程序**
   - 确保已安装Java 8或更高版本
   - 检查JAR文件是否完整下载

2. **扫描失败**
   - 检查网络连接
   - 确认域名拼写正确
   - 某些域名可能不支持SSL或证书配置有问题

3. **相关域名数量为0**
   - 可能该证书只用于单个域名
   - 或者API查询暂时不可用

### 日志文件

应用程序会在运行目录下的 `logs/` 文件夹中生成日志文件，可以查看详细的错误信息。

## 技术架构

- **语言**: Java 8
- **GUI框架**: Swing
- **HTTP客户端**: Apache HttpClient
- **JSON处理**: Jackson
- **CSV处理**: Apache Commons CSV
- **构建工具**: Maven

## 开发和贡献

如果您想参与开发或报告问题：

1. 查看源代码结构：
   ```
   src/main/java/com/certscanner/
   ├── CertificateScannerApp.java          # 主应用程序
   ├── model/
   │   └── CertificateInfo.java            # 证书信息模型
   ├── service/
   │   ├── CertificateService.java         # 证书获取服务
   │   ├── CertificateSearchService.java   # 证书搜索服务
   │   └── CertificateScannerService.java  # 主扫描服务
   └── gui/
       ├── MainWindow.java                 # 主窗口
       └── ResultsPanel.java               # 结果展示面板
   ```

2. 运行测试：
   ```bash
   mvn test
   ```

## 许可证

本项目采用开源许可证，具体请查看LICENSE文件。

## 版本历史

- **v1.0.0**: 初始版本
  - 基本的SSL证书扫描功能
  - 图形化用户界面
  - 批量扫描支持
  - 结果导出功能
