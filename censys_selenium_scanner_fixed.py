"""
修复版本的Censys Selenium扫描器
主要修复了域名提取逻辑，避免提取JavaScript代码片段
"""

import time
import argparse
import re
from urllib.parse import quote
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup


class CensysSeleniumScanner:
    def __init__(self, cookie=None, verbose=True, headless=True):
        self.cookie = cookie
        self.verbose = verbose
        self.headless = headless
        self.driver = None
    
    def log(self, message, level="INFO"):
        if self.verbose:
            print(f"[{level}] {message}")
    
    def setup_driver(self):
        """设置Chrome WebDriver"""
        try:
            options = Options()
            if self.headless:
                options.add_argument('--headless')
            
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            self.driver = webdriver.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.log("Chrome WebDriver设置成功")
            return True
            
        except Exception as e:
            self.log(f"设置WebDriver失败: {e}", "ERROR")
            return False
    
    def set_cookies(self):
        """设置cookies"""
        if not self.cookie:
            self.log("未提供cookie，可能无法访问完整结果", "WARN")
            return
        
        try:
            self.log("设置cookies...")
            self.driver.get("https://platform.censys.io/")
            
            # 解析cookie字符串
            cookie_pairs = self.cookie.split(';')
            for cookie_pair in cookie_pairs:
                if '=' in cookie_pair:
                    name, value = cookie_pair.strip().split('=', 1)
                    self.driver.add_cookie({
                        'name': name,
                        'value': value,
                        'domain': '.censys.io',
                        'path': '/'
                    })
            
            self.log("Cookies设置完成")
            
        except Exception as e:
            self.log(f"设置cookies失败: {e}", "ERROR")
    
    def search_censys(self, sha256):
        """在Censys平台搜索SHA256证书"""
        if not self.setup_driver():
            return {"error": "WebDriver设置失败", "all_names": [], "html": ""}
        
        try:
            # 设置cookies
            self.set_cookies()
            
            # 构建搜索URL
            search_query = f'cert.fingerprint_sha256 = "{sha256}"'
            search_url = f"https://platform.censys.io/search?q={quote(search_query)}"
            
            self.log(f"访问搜索URL: {search_url}")
            self.driver.get(search_url)
            
            # 等待页面加载
            time.sleep(5)
            
            # 检查是否遇到Cloudflare验证
            if "正在验证您是否是真人" in self.driver.page_source or "Just a moment" in self.driver.page_source:
                self.log("检测到Cloudflare验证，等待通过...")
                
                # 等待验证通过
                for i in range(60):
                    time.sleep(1)
                    if "正在验证您是否是真人" not in self.driver.page_source and "Just a moment" not in self.driver.page_source:
                        self.log("Cloudflare验证已通过")
                        break
                    if i % 10 == 0:
                        self.log(f"仍在等待Cloudflare验证... ({i+1}/60秒)")
            
            # 等待搜索结果加载
            self.log("等待搜索结果...")
            time.sleep(5)
            
            # 获取页面HTML
            html_content = self.driver.page_source
            
            # 提取域名和IP
            all_names = self.extract_all_names_fixed(html_content)
            
            return {
                "error": None,
                "all_names": all_names,
                "html": html_content
            }
            
        except Exception as e:
            self.log(f"搜索过程中发生错误: {e}", "ERROR")
            return {"error": str(e), "all_names": [], "html": ""}
        
        finally:
            if self.driver:
                self.driver.quit()
    
    def extract_all_names_fixed(self, html_content):
        """修复版本的域名提取方法"""
        all_names = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 移除script和style标签，避免提取JavaScript代码
            for script in soup(["script", "style"]):
                script.decompose()
            
            # 查找包含搜索结果的特定区域
            result_containers = soup.find_all(['div', 'section', 'article'], 
                                            class_=re.compile(r'result|search|certificate|host', re.I))
            
            if not result_containers:
                # 如果没有找到特定容器，查找表格行
                result_containers = soup.find_all('tr')
            
            self.log(f"找到 {len(result_containers)} 个可能的结果容器")
            
            for container in result_containers:
                # 获取容器的纯文本内容
                text_content = container.get_text()
                
                # 查找域名（更严格的模式）
                domain_matches = re.findall(r'\b([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}\b', text_content)
                for match in domain_matches:
                    domain = match[0] if isinstance(match, tuple) else match
                    if self.is_valid_domain_or_ip_strict(domain):
                        all_names.append(domain)
                
                # 查找IP地址
                ip_matches = re.findall(r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b', text_content)
                for ip in ip_matches:
                    if self.is_valid_domain_or_ip_strict(ip):
                        all_names.append(ip)
            
            # 去重并过滤
            all_names = list(set(all_names))
            
            # 更严格的过滤
            filtered_names = []
            exclude_patterns = [
                'censys.io', 'google.com', 'cloudflare.com', 'amazonaws.com',
                'gstatic.com', 'googleapis.com', 'doubleclick.net', 'googlesyndication.com',
                'example.com', 'localhost', '127.0.0.1', '0.0.0.0', 'mozilla.org',
                'w3.org', 'schema.org', 'javascript', 'css', 'html'
            ]
            
            for name in all_names:
                # 检查是否包含排除模式
                if not any(exclude in name.lower() for exclude in exclude_patterns):
                    # 排除明显的文件名和JavaScript相关内容
                    if not name.endswith(('.js', '.css', '.png', '.jpg', '.gif', '.svg', '.ico')):
                        # 排除包含JavaScript关键字的内容
                        if not any(keyword in name.lower() for keyword in ['function', 'var', 'let', 'const', 'document', 'window', 'location', 'history']):
                            # 确保域名格式正确
                            if '.' in name and len(name.split('.')) >= 2:
                                filtered_names.append(name)
            
            self.log(f"提取到 {len(filtered_names)} 个有效的域名/IP")
            return sorted(filtered_names)
            
        except Exception as e:
            self.log(f"提取域名失败: {e}", "ERROR")
            return []
    
    def is_valid_domain_or_ip_strict(self, text):
        """更严格的域名/IP验证"""
        if not text or len(text) > 100 or len(text) < 3:
            return False
        
        # 检查是否包含点号
        if '.' not in text:
            return False
        
        # 排除明显不是域名的文本
        invalid_chars = ['<', '>', '"', "'", '(', ')', '[', ']', '{', '}', ' ', '\n', '\t', '\\', '/', '=', '?', '&', '#']
        if any(char in text for char in invalid_chars):
            return False
        
        # 排除JavaScript相关的内容
        js_keywords = ['function', 'var', 'let', 'const', 'document', 'window', 'location', 'history', 'onload', 'src', 'href']
        if any(keyword in text.lower() for keyword in js_keywords):
            return False
        
        # 检查是否以数字开头（可能是IP）
        parts = text.split('.')
        
        # IP地址检查
        if len(parts) == 4:
            try:
                for part in parts:
                    if not part.isdigit():
                        break
                    num = int(part)
                    if not (0 <= num <= 255):
                        break
                else:
                    return True
            except ValueError:
                pass
        
        # 域名检查
        if len(parts) >= 2:
            # 检查每个部分
            for part in parts:
                if not part:
                    return False
                # 域名部分只能包含字母、数字和连字符
                if not re.match(r'^[a-zA-Z0-9-]+$', part):
                    return False
                # 不能以连字符开头或结尾
                if part.startswith('-') or part.endswith('-'):
                    return False
                # 不能全是数字（除非是IP地址）
                if part.isdigit() and len(parts) != 4:
                    return False
            
            # 顶级域名检查
            tld = parts[-1]
            if len(tld) < 2 or not tld.isalpha():
                return False
            
            return True
        
        return False
