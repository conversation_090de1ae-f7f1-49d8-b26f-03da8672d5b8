#!/usr/bin/env python3
"""
完整的证书扫描工具
用法: python3 crt-scan.py mi.com --cookie='xxx'

流程:
1. 查询目标域名的证书信息 (crt.sh)
2. 获取证书的SHA-256指纹
3. 使用Selenium在Censys平台搜索使用相同证书的所有网站
"""

import re
import time
import argparse
import sys
from urllib.parse import urlparse, quote
from curl_cffi import requests
from bs4 import BeautifulSoup

# 导入Selenium扫描器
from censys_selenium_scanner_fixed import CensysSeleniumScanner

class CertificateScanner:
    def __init__(self, cookie=None, verbose=True):
        """
        初始化证书扫描器
        
        Args:
            cookie (str): Censys平台的cookie字符串
            verbose (bool): 是否显示详细信息
        """
        self.session = requests.Session(impersonate="chrome120")
        self.cookie = cookie
        self.verbose = verbose
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

    def log(self, message, level="INFO"):
        """输出日志信息"""
        if self.verbose:
            timestamp = time.strftime('%H:%M:%S')
            print(f"[{timestamp}] {level}: {message}")

    def extract_domain(self, url):
        """从URL中提取域名"""
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        parsed = urlparse(url)
        return parsed.netloc

    def query_crt_sh(self, domain):
        """
        步骤1: 查询crt.sh获取证书信息
        
        Args:
            domain (str): 目标域名
            
        Returns:
            tuple: (first_id, last_id) 第一个和最后一个证书ID
        """
        self.log(f"步骤1: 正在查询 {domain} 的证书信息...")
        
        url = f"https://crt.sh/?q={quote(domain)}"
        
        # 尝试多次请求，增加超时时间
        for attempt in range(3):
            try:
                self.log(f"尝试第 {attempt + 1} 次请求...")
                response = self.session.get(url, timeout=60)
                response.raise_for_status()

                self.log(f"crt.sh响应状态码: {response.status_code}")

                # 解析HTML获取证书ID
                soup = BeautifulSoup(response.text, 'html.parser')

                # 查找包含证书ID的表格
                cert_ids = []
                for link in soup.find_all('a', href=True):
                    href = link.get('href')
                    if href and '?id=' in href:
                        cert_id = href.split('?id=')[1]
                        if cert_id.isdigit():
                            cert_ids.append(cert_id)

                if not cert_ids:
                    self.log(f"未找到 {domain} 的证书信息", "ERROR")
                    return None, None

                first_id = cert_ids[0]
                last_id = cert_ids[-1]

                self.log(f"找到 {len(cert_ids)} 个证书")
                self.log(f"第一个证书ID: {first_id}")
                self.log(f"最后一个证书ID: {last_id}")

                return first_id, last_id

            except Exception as e:
                self.log(f"第 {attempt + 1} 次尝试失败: {e}", "WARN")
                if attempt < 2:  # 不是最后一次尝试
                    self.log("等待5秒后重试...")
                    time.sleep(5)
                else:
                    self.log(f"查询crt.sh失败，已尝试3次", "ERROR")
                    return None, None

    def get_cert_sha256(self, cert_id):
        """
        步骤2: 根据证书ID获取SHA-256指纹
        
        Args:
            cert_id (str): 证书ID
            
        Returns:
            str: SHA-256指纹
        """
        self.log(f"步骤2: 正在获取证书ID {cert_id} 的SHA-256指纹...")
        
        url = f"https://crt.sh/?id={cert_id}"
        
        # 尝试多次请求
        for attempt in range(3):
            try:
                self.log(f"尝试第 {attempt + 1} 次请求证书详情...")
                response = self.session.get(url, timeout=60)
                response.raise_for_status()

                self.log(f"证书详情响应状态码: {response.status_code}")

                # 查找SHA-256指纹
                sha256_pattern = r'SHA-256.*?([a-fA-F0-9]{64})'
                match = re.search(sha256_pattern, response.text, re.IGNORECASE | re.DOTALL)

                if match:
                    sha256 = match.group(1).lower()
                    self.log(f"找到SHA-256指纹: {sha256}")
                    return sha256
                else:
                    self.log(f"未找到证书ID {cert_id} 的SHA-256指纹", "ERROR")
                    return None

            except Exception as e:
                self.log(f"第 {attempt + 1} 次尝试失败: {e}", "WARN")
                if attempt < 2:  # 不是最后一次尝试
                    self.log("等待3秒后重试...")
                    time.sleep(3)
                else:
                    self.log(f"获取SHA-256指纹失败，已尝试3次", "ERROR")
                    return None

    def search_censys_with_selenium(self, sha256):
        """
        步骤3: 使用Selenium在Censys平台搜索使用相同证书的域名
        
        Args:
            sha256 (str): 证书SHA-256指纹
            
        Returns:
            list: 使用相同证书的域名列表
        """
        self.log(f"步骤3: 正在Censys平台搜索使用证书 {sha256} 的所有网站...")
        
        if not self.cookie:
            self.log("警告: 未提供Censys cookie，可能无法获取完整结果", "WARN")
        
        # 使用Selenium扫描器
        scanner = CensysSeleniumScanner(
            cookie=self.cookie,
            verbose=self.verbose,
            headless=True
        )
        
        result = scanner.search_censys(sha256)
        
        if result["error"]:
            self.log(f"Censys搜索失败: {result['error']}", "ERROR")
            return []
        
        return result["all_names"]

    def scan_domain(self, domain):
        """
        完整的扫描流程
        
        Args:
            domain (str): 目标域名
            
        Returns:
            dict: 包含扫描结果的字典
        """
        domain = self.extract_domain(domain)
        
        print("=" * 80)
        print(f"🔍 开始扫描域名: {domain}")
        print("=" * 80)
        
        # 步骤1: 查询crt.sh获取证书ID
        first_id, last_id = self.query_crt_sh(domain)
        if not first_id:
            return {
                "success": False,
                "error": "无法获取证书信息",
                "domain": domain,
                "cert_ids": [],
                "sha256": None,
                "related_domains": []
            }
        
        # 步骤2: 获取SHA-256指纹（优先使用第一个ID）
        sha256 = self.get_cert_sha256(first_id)
        if not sha256 and last_id != first_id:
            # 如果第一个失败，尝试最后一个
            self.log("尝试使用最后一个证书ID...")
            sha256 = self.get_cert_sha256(last_id)
        
        if not sha256:
            return {
                "success": False,
                "error": "无法获取SHA-256指纹",
                "domain": domain,
                "cert_ids": [first_id, last_id],
                "sha256": None,
                "related_domains": []
            }
        
        # 步骤3: 在Censys搜索使用相同证书的域名
        related_domains = self.search_censys_with_selenium(sha256)
        
        return {
            "success": True,
            "error": None,
            "domain": domain,
            "cert_ids": [first_id, last_id],
            "sha256": sha256,
            "related_domains": related_domains
        }


def main():
    parser = argparse.ArgumentParser(
        description='完整的证书扫描工具 - 查询使用相同证书的所有网站',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python3 crt-scan.py mi.com --cookie='auth_v1=xxx...'
  python3 crt-scan.py https://www.baidu.com --cookie='auth_v1=xxx...' --output results.txt
  python3 crt-scan.py example.com --no-headless  # 显示浏览器窗口（调试用）

流程说明:
  1. 查询目标域名的证书信息 (crt.sh)
  2. 获取证书的SHA-256指纹
  3. 在Censys平台搜索使用相同证书的所有网站
        """
    )
    
    parser.add_argument('domain', help='目标域名或URL')
    parser.add_argument('--cookie', required=True, help='Censys平台的cookie字符串')
    parser.add_argument('--output', '-o', help='输出结果到文件')
    parser.add_argument('--verbose', '-v', action='store_true', default=True, help='显示详细信息')
    parser.add_argument('--quiet', '-q', action='store_true', help='静默模式，只输出结果')
    parser.add_argument('--no-headless', action='store_true', help='显示浏览器窗口（调试用）')
    
    args = parser.parse_args()
    
    # 静默模式
    if args.quiet:
        args.verbose = False
    
    # 创建扫描器实例
    scanner = CertificateScanner(
        cookie=args.cookie,
        verbose=args.verbose
    )
    
    # 执行扫描
    result = scanner.scan_domain(args.domain)
    
    # 输出结果
    if not result["success"]:
        if not args.quiet:
            print(f"\n❌ 扫描失败: {result['error']}")
        sys.exit(1)
    
    # 成功结果
    domain = result["domain"]
    sha256 = result["sha256"]
    related_domains = result["related_domains"]
    
    if not args.quiet:
        print("\n" + "=" * 80)
        print("📋 扫描结果汇总")
        print("=" * 80)
        print(f"🎯 目标域名: {domain}")
        print(f"🔑 证书SHA-256: {sha256}")
        print(f"📊 找到 {len(related_domains)} 个使用相同证书的网站")
        print("=" * 80)
    
    if related_domains:
        if not args.quiet:
            print("🌐 使用相同证书的所有网站:")
            print("-" * 40)
        
        for i, site in enumerate(related_domains, 1):
            if args.quiet:
                print(site)
            else:
                print(f"{i:3d}. {site}")
        
        # 保存到文件
        if args.output:
            try:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(f"证书扫描结果报告\n")
                    f.write(f"{'=' * 50}\n")
                    f.write(f"目标域名: {domain}\n")
                    f.write(f"证书SHA-256: {sha256}\n")
                    f.write(f"扫描时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"找到 {len(related_domains)} 个使用相同证书的网站:\n\n")
                    for i, site in enumerate(related_domains, 1):
                        f.write(f"{i:3d}. {site}\n")
                
                if not args.quiet:
                    print(f"\n💾 结果已保存到: {args.output}")
            except Exception as e:
                if not args.quiet:
                    print(f"❌ 保存文件失败: {e}")
    else:
        if not args.quiet:
            print("❌ 未找到使用相同证书的其他网站")
    
    if not args.quiet:
        print(f"\n✅ 扫描完成!")


if __name__ == "__main__":
    main()
