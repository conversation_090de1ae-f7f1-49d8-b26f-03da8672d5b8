@echo off
chcp 65001 >nul

echo SSL证书扫描器启动脚本

REM 检查Java是否安装
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java。请安装Java 8或更高版本。
    pause
    exit /b 1
)

REM 显示Java版本
echo 检测到Java版本:
java -version

REM 创建日志目录
if not exist logs mkdir logs

REM 设置JVM参数
set JVM_OPTS=-Xmx512m -Dfile.encoding=UTF-8

REM 启动应用程序
echo.
echo 启动SSL证书扫描器...
java %JVM_OPTS% -jar target\certificate-scanner-1.0.0.jar

echo.
echo 应用程序已退出。
pause
