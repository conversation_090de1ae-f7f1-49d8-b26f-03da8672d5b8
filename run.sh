#!/bin/bash

# SSL证书扫描器启动脚本

# 检查Java是否安装
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java。请安装Java 8或更高版本。"
    exit 1
fi

# 检查Java版本
JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
echo "检测到Java版本: $JAVA_VERSION"

# 创建日志目录
mkdir -p logs

# 设置JVM参数
JVM_OPTS="-Xmx512m -Dfile.encoding=UTF-8"

# 启动应用程序
echo "启动SSL证书扫描器..."
java $JVM_OPTS -jar target/certificate-scanner-1.0.0.jar

echo "应用程序已退出。"
