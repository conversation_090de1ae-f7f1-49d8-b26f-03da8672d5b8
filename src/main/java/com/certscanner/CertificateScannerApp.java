package com.certscanner;

import com.certscanner.gui.MainWindow;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;

/**
 * SSL证书扫描器主应用程序
 */
public class CertificateScannerApp {
    private static final Logger logger = LoggerFactory.getLogger(CertificateScannerApp.class);

    public static void main(String[] args) {
        // 设置系统属性
        System.setProperty("java.awt.headless", "false");
        
        // 设置Look and Feel
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
        } catch (Exception e) {
            logger.warn("无法设置系统Look and Feel，使用默认样式: {}", e.getMessage());
        }

        // 设置字体（支持中文显示）
        setupFonts();

        // 在EDT中启动GUI
        SwingUtilities.invokeLater(() -> {
            try {
                logger.info("启动SSL证书扫描器应用程序");
                
                // 创建并显示主窗口
                MainWindow mainWindow = new MainWindow();
                mainWindow.setVisible(true);
                
                logger.info("应用程序启动完成");
                
            } catch (Exception e) {
                logger.error("启动应用程序时发生错误", e);
                JOptionPane.showMessageDialog(null, 
                    "启动应用程序时发生错误: " + e.getMessage(), 
                    "启动错误", 
                    JOptionPane.ERROR_MESSAGE);
                System.exit(1);
            }
        });
    }

    /**
     * 设置字体以支持中文显示
     */
    private static void setupFonts() {
        try {
            // 获取系统默认字体
            Font defaultFont = new Font(Font.SANS_SERIF, Font.PLAIN, 12);
            
            // 设置UI默认字体
            UIManager.put("Label.font", defaultFont);
            UIManager.put("Button.font", defaultFont);
            UIManager.put("TextField.font", defaultFont);
            UIManager.put("TextArea.font", defaultFont);
            UIManager.put("Table.font", defaultFont);
            UIManager.put("TableHeader.font", defaultFont);
            UIManager.put("Menu.font", defaultFont);
            UIManager.put("MenuItem.font", defaultFont);
            UIManager.put("TitledBorder.font", defaultFont);
            UIManager.put("ProgressBar.font", defaultFont);
            
        } catch (Exception e) {
            logger.warn("设置字体时发生错误: {}", e.getMessage());
        }
    }
}
