package com.certscanner.gui;

import com.certscanner.model.CertificateInfo;
import com.certscanner.service.CertificateScannerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 主窗口类
 */
public class MainWindow extends JFrame {
    private static final Logger logger = LoggerFactory.getLogger(MainWindow.class);
    
    private final CertificateScannerService scannerService;
    private JTextField domainField;
    private JTextArea domainListArea;
    private JButton scanSingleButton;
    private JButton scanBatchButton;
    private JButton loadFileButton;
    private JButton clearButton;
    private JProgressBar progressBar;
    private JLabel statusLabel;
    private ResultsPanel resultsPanel;

    public MainWindow() {
        this.scannerService = new CertificateScannerService();
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        
        // 设置窗口关闭时清理资源
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                scannerService.shutdown();
                System.exit(0);
            }
        });
    }

    private void initializeComponents() {
        setTitle("SSL证书扫描器 v1.0");
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        setSize(1000, 700);
        setLocationRelativeTo(null);
        
        // 设置Look and Feel
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
        } catch (Exception e) {
            logger.warn("无法设置系统Look and Feel: {}", e.getMessage());
        }

        // 初始化组件
        domainField = new JTextField(30);
        domainListArea = new JTextArea(8, 50);
        domainListArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        domainListArea.setBorder(BorderFactory.createLoweredBevelBorder());
        
        scanSingleButton = new JButton("扫描单个域名");
        scanBatchButton = new JButton("批量扫描");
        loadFileButton = new JButton("从文件加载");
        clearButton = new JButton("清空");
        
        progressBar = new JProgressBar(0, 100);
        progressBar.setStringPainted(true);
        progressBar.setString("就绪");
        
        statusLabel = new JLabel("就绪");
        statusLabel.setBorder(new EmptyBorder(5, 5, 5, 5));
        
        resultsPanel = new ResultsPanel();
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // 顶部输入面板
        JPanel inputPanel = createInputPanel();
        add(inputPanel, BorderLayout.NORTH);

        // 中间结果面板
        add(resultsPanel, BorderLayout.CENTER);

        // 底部状态面板
        JPanel statusPanel = createStatusPanel();
        add(statusPanel, BorderLayout.SOUTH);
    }

    private JPanel createInputPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("输入"));

        // 单个域名输入
        JPanel singlePanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        singlePanel.add(new JLabel("域名:"));
        singlePanel.add(domainField);
        singlePanel.add(scanSingleButton);

        // 批量域名输入
        JPanel batchPanel = new JPanel(new BorderLayout());
        batchPanel.setBorder(BorderFactory.createTitledBorder("批量域名 (每行一个)"));
        
        JScrollPane scrollPane = new JScrollPane(domainListArea);
        batchPanel.add(scrollPane, BorderLayout.CENTER);
        
        JPanel batchButtonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        batchButtonPanel.add(loadFileButton);
        batchButtonPanel.add(clearButton);
        batchButtonPanel.add(scanBatchButton);
        batchPanel.add(batchButtonPanel, BorderLayout.SOUTH);

        panel.add(singlePanel, BorderLayout.NORTH);
        panel.add(batchPanel, BorderLayout.CENTER);

        return panel;
    }

    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEtchedBorder());
        
        panel.add(progressBar, BorderLayout.CENTER);
        panel.add(statusLabel, BorderLayout.EAST);
        
        return panel;
    }

    private void setupEventHandlers() {
        // 单个域名扫描
        scanSingleButton.addActionListener(e -> {
            String domain = domainField.getText().trim();
            if (domain.isEmpty()) {
                JOptionPane.showMessageDialog(this, "请输入域名", "错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
            scanSingleDomain(domain);
        });

        // 批量扫描
        scanBatchButton.addActionListener(e -> {
            List<String> domains = getDomainListFromTextArea();
            if (domains.isEmpty()) {
                JOptionPane.showMessageDialog(this, "请输入域名列表", "错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
            scanMultipleDomains(domains);
        });

        // 从文件加载
        loadFileButton.addActionListener(e -> loadDomainsFromFile());

        // 清空
        clearButton.addActionListener(e -> {
            domainListArea.setText("");
            resultsPanel.clearResults();
        });

        // 回车键扫描单个域名
        domainField.addActionListener(e -> scanSingleButton.doClick());
    }

    private void scanSingleDomain(String domain) {
        setUIEnabled(false);
        updateStatus("正在扫描域名: " + domain);
        progressBar.setIndeterminate(true);

        SwingWorker<CertificateInfo, Void> worker = new SwingWorker<CertificateInfo, Void>() {
            @Override
            protected CertificateInfo doInBackground() throws Exception {
                return scannerService.scanDomain(domain);
            }

            @Override
            protected void done() {
                try {
                    CertificateInfo result = get();
                    if (result != null) {
                        resultsPanel.addResult(result);
                        updateStatus("扫描完成: " + domain);
                    } else {
                        updateStatus("扫描失败: " + domain);
                        JOptionPane.showMessageDialog(MainWindow.this, 
                            "无法获取域名 " + domain + " 的证书信息", 
                            "扫描失败", JOptionPane.WARNING_MESSAGE);
                    }
                } catch (Exception e) {
                    logger.error("扫描域名时发生错误", e);
                    updateStatus("扫描出错: " + e.getMessage());
                    JOptionPane.showMessageDialog(MainWindow.this, 
                        "扫描时发生错误: " + e.getMessage(), 
                        "错误", JOptionPane.ERROR_MESSAGE);
                } finally {
                    progressBar.setIndeterminate(false);
                    progressBar.setValue(0);
                    setUIEnabled(true);
                }
            }
        };

        worker.execute();
    }

    private void scanMultipleDomains(List<String> domains) {
        setUIEnabled(false);
        updateStatus("开始批量扫描 " + domains.size() + " 个域名");
        progressBar.setValue(0);
        progressBar.setMaximum(domains.size());

        SwingWorker<List<CertificateInfo>, String> worker = new SwingWorker<List<CertificateInfo>, String>() {
            @Override
            protected List<CertificateInfo> doInBackground() throws Exception {
                return scannerService.scanDomains(domains, (completed, total, currentDomain) -> {
                    SwingUtilities.invokeLater(() -> {
                        progressBar.setValue(completed);
                        publish("正在扫描: " + currentDomain + " (" + completed + "/" + total + ")");
                    });
                });
            }

            @Override
            protected void process(List<String> chunks) {
                if (!chunks.isEmpty()) {
                    updateStatus(chunks.get(chunks.size() - 1));
                }
            }

            @Override
            protected void done() {
                try {
                    List<CertificateInfo> results = get();
                    resultsPanel.addResults(results);
                    updateStatus("批量扫描完成，成功扫描 " + results.size() + " 个域名");
                } catch (Exception e) {
                    logger.error("批量扫描时发生错误", e);
                    updateStatus("批量扫描出错: " + e.getMessage());
                    JOptionPane.showMessageDialog(MainWindow.this, 
                        "批量扫描时发生错误: " + e.getMessage(), 
                        "错误", JOptionPane.ERROR_MESSAGE);
                } finally {
                    progressBar.setValue(0);
                    setUIEnabled(true);
                }
            }
        };

        worker.execute();
    }

    private List<String> getDomainListFromTextArea() {
        String text = domainListArea.getText().trim();
        if (text.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> domains = new ArrayList<>();
        String[] lines = text.split("\n");

        for (String line : lines) {
            line = line.trim();
            if (!line.isEmpty() && !line.startsWith("#")) {
                // 支持逗号分隔的域名
                String[] parts = line.split("[,\\s]+");
                for (String part : parts) {
                    part = part.trim();
                    if (!part.isEmpty()) {
                        domains.add(part);
                    }
                }
            }
        }

        return domains;
    }

    private void loadDomainsFromFile() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileFilter(new FileNameExtensionFilter("文本文件 (*.txt)", "txt"));
        fileChooser.setFileFilter(new FileNameExtensionFilter("所有文件", "*"));

        int result = fileChooser.showOpenDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            File file = fileChooser.getSelectedFile();
            try {
                StringBuilder content = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        content.append(line).append("\n");
                    }
                }

                domainListArea.setText(content.toString());
                updateStatus("已加载文件: " + file.getName());

            } catch (IOException e) {
                logger.error("读取文件时发生错误", e);
                JOptionPane.showMessageDialog(this,
                    "读取文件时发生错误: " + e.getMessage(),
                    "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    private void setUIEnabled(boolean enabled) {
        domainField.setEnabled(enabled);
        domainListArea.setEnabled(enabled);
        scanSingleButton.setEnabled(enabled);
        scanBatchButton.setEnabled(enabled);
        loadFileButton.setEnabled(enabled);
        clearButton.setEnabled(enabled);
    }

    private void updateStatus(String message) {
        statusLabel.setText(message);
        logger.info(message);
    }
}
