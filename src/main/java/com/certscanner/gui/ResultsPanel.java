package com.certscanner.gui;

import com.certscanner.model.CertificateInfo;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableRowSorter;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 结果展示面板
 */
public class ResultsPanel extends JPanel {
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private JTable resultsTable;
    private DefaultTableModel tableModel;
    private JButton exportButton;
    private JButton clearResultsButton;
    private JLabel resultCountLabel;

    public ResultsPanel() {
        initializeComponents();
        setupLayout();
        setupEventHandlers();
    }

    private void initializeComponents() {
        // 创建表格模型
        String[] columnNames = {
            "域名", "颁发者", "有效期至", "SHA256指纹", "相关域名数量", "状态"
        };
        
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // 所有单元格不可编辑
            }
        };

        resultsTable = new JTable(tableModel);
        resultsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        resultsTable.setAutoCreateRowSorter(true);
        
        // 设置列宽
        resultsTable.getColumnModel().getColumn(0).setPreferredWidth(200); // 域名
        resultsTable.getColumnModel().getColumn(1).setPreferredWidth(250); // 颁发者
        resultsTable.getColumnModel().getColumn(2).setPreferredWidth(150); // 有效期
        resultsTable.getColumnModel().getColumn(3).setPreferredWidth(100); // 指纹
        resultsTable.getColumnModel().getColumn(4).setPreferredWidth(100); // 相关域名数
        resultsTable.getColumnModel().getColumn(5).setPreferredWidth(80);  // 状态

        exportButton = new JButton("导出结果");
        clearResultsButton = new JButton("清空结果");
        resultCountLabel = new JLabel("结果: 0 条");
    }

    private void setupLayout() {
        setLayout(new BorderLayout());
        setBorder(BorderFactory.createTitledBorder("扫描结果"));

        // 表格滚动面板
        JScrollPane scrollPane = new JScrollPane(resultsTable);
        scrollPane.setPreferredSize(new Dimension(800, 300));
        add(scrollPane, BorderLayout.CENTER);

        // 底部按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.add(resultCountLabel);
        buttonPanel.add(Box.createHorizontalStrut(20));
        buttonPanel.add(exportButton);
        buttonPanel.add(clearResultsButton);
        add(buttonPanel, BorderLayout.SOUTH);
    }

    private void setupEventHandlers() {
        // 双击查看详细信息
        resultsTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    int row = resultsTable.getSelectedRow();
                    if (row >= 0) {
                        showDetailDialog(row);
                    }
                }
            }
        });

        // 导出按钮
        exportButton.addActionListener(e -> exportResults());

        // 清空结果按钮
        clearResultsButton.addActionListener(e -> clearResults());
    }

    public void addResult(CertificateInfo certInfo) {
        SwingUtilities.invokeLater(() -> {
            Object[] rowData = createRowData(certInfo);
            tableModel.addRow(rowData);
            updateResultCount();
        });
    }

    public void addResults(List<CertificateInfo> certInfos) {
        SwingUtilities.invokeLater(() -> {
            for (CertificateInfo certInfo : certInfos) {
                Object[] rowData = createRowData(certInfo);
                tableModel.addRow(rowData);
            }
            updateResultCount();
        });
    }

    public void clearResults() {
        SwingUtilities.invokeLater(() -> {
            tableModel.setRowCount(0);
            updateResultCount();
        });
    }

    private Object[] createRowData(CertificateInfo certInfo) {
        String domain = certInfo.getDomain();
        String issuer = truncateString(certInfo.getIssuer(), 50);
        String notAfter = certInfo.getNotAfter() != null ? 
            certInfo.getNotAfter().format(DATE_FORMATTER) : "未知";
        String fingerprint = truncateString(certInfo.getSha256Fingerprint(), 16);
        int relatedCount = certInfo.getRelatedDomains() != null ? 
            certInfo.getRelatedDomains().size() : 0;
        String status = "正常";

        // 检查证书是否即将过期
        if (certInfo.getNotAfter() != null) {
            long daysUntilExpiry = java.time.temporal.ChronoUnit.DAYS.between(
                java.time.LocalDateTime.now(), certInfo.getNotAfter());
            if (daysUntilExpiry < 0) {
                status = "已过期";
            } else if (daysUntilExpiry < 30) {
                status = "即将过期";
            }
        }

        return new Object[]{domain, issuer, notAfter, fingerprint, relatedCount, status};
    }

    private String truncateString(String str, int maxLength) {
        if (str == null) return "";
        if (str.length() <= maxLength) return str;
        return str.substring(0, maxLength - 3) + "...";
    }

    private void updateResultCount() {
        int count = tableModel.getRowCount();
        resultCountLabel.setText("结果: " + count + " 条");
    }

    private void showDetailDialog(int row) {
        // 获取原始行索引（考虑排序）
        int modelRow = resultsTable.convertRowIndexToModel(row);
        
        String domain = (String) tableModel.getValueAt(modelRow, 0);
        String issuer = (String) tableModel.getValueAt(modelRow, 1);
        String notAfter = (String) tableModel.getValueAt(modelRow, 2);
        String fingerprint = (String) tableModel.getValueAt(modelRow, 3);
        int relatedCount = (Integer) tableModel.getValueAt(modelRow, 4);

        // 创建详细信息对话框
        JDialog dialog = new JDialog((Frame) SwingUtilities.getWindowAncestor(this), 
                                   "证书详细信息 - " + domain, true);
        dialog.setSize(600, 400);
        dialog.setLocationRelativeTo(this);

        JTextArea textArea = new JTextArea();
        textArea.setEditable(false);
        textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        
        StringBuilder details = new StringBuilder();
        details.append("域名: ").append(domain).append("\n");
        details.append("颁发者: ").append(issuer).append("\n");
        details.append("有效期至: ").append(notAfter).append("\n");
        details.append("SHA256指纹: ").append(fingerprint).append("\n");
        details.append("相关域名数量: ").append(relatedCount).append("\n\n");
        
        if (relatedCount > 0) {
            details.append("相关域名:\n");
            details.append("(双击表格行查看完整的相关域名列表)\n");
        }

        textArea.setText(details.toString());

        JScrollPane scrollPane = new JScrollPane(textArea);
        dialog.add(scrollPane);
        dialog.setVisible(true);
    }

    private void exportResults() {
        if (tableModel.getRowCount() == 0) {
            JOptionPane.showMessageDialog(this, "没有结果可以导出", "提示", JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setSelectedFile(new File("certificate_scan_results.csv"));
        
        int result = fileChooser.showSaveDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            File file = fileChooser.getSelectedFile();
            try {
                exportToCSV(file);
                JOptionPane.showMessageDialog(this, 
                    "结果已导出到: " + file.getAbsolutePath(), 
                    "导出成功", JOptionPane.INFORMATION_MESSAGE);
            } catch (IOException e) {
                JOptionPane.showMessageDialog(this, 
                    "导出失败: " + e.getMessage(), 
                    "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    private void exportToCSV(File file) throws IOException {
        try (FileWriter writer = new FileWriter(file);
             CSVPrinter printer = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(
                 "域名", "颁发者", "有效期至", "SHA256指纹", "相关域名数量", "状态"))) {
            
            for (int i = 0; i < tableModel.getRowCount(); i++) {
                printer.printRecord(
                    tableModel.getValueAt(i, 0),
                    tableModel.getValueAt(i, 1),
                    tableModel.getValueAt(i, 2),
                    tableModel.getValueAt(i, 3),
                    tableModel.getValueAt(i, 4),
                    tableModel.getValueAt(i, 5)
                );
            }
        }
    }
}
