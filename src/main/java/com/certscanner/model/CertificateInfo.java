package com.certscanner.model;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 证书信息模型类
 */
public class CertificateInfo {
    private String domain;
    private String issuer;
    private String subject;
    private String serialNumber;
    private String sha1Fingerprint;
    private String sha256Fingerprint;
    private LocalDateTime notBefore;
    private LocalDateTime notAfter;
    private List<String> subjectAlternativeNames;
    private List<String> relatedDomains;

    public CertificateInfo() {}

    public CertificateInfo(String domain) {
        this.domain = domain;
    }

    // Getters and Setters
    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getSha1Fingerprint() {
        return sha1Fingerprint;
    }

    public void setSha1Fingerprint(String sha1Fingerprint) {
        this.sha1Fingerprint = sha1Fingerprint;
    }

    public String getSha256Fingerprint() {
        return sha256Fingerprint;
    }

    public void setSha256Fingerprint(String sha256Fingerprint) {
        this.sha256Fingerprint = sha256Fingerprint;
    }

    public LocalDateTime getNotBefore() {
        return notBefore;
    }

    public void setNotBefore(LocalDateTime notBefore) {
        this.notBefore = notBefore;
    }

    public LocalDateTime getNotAfter() {
        return notAfter;
    }

    public void setNotAfter(LocalDateTime notAfter) {
        this.notAfter = notAfter;
    }

    public List<String> getSubjectAlternativeNames() {
        return subjectAlternativeNames;
    }

    public void setSubjectAlternativeNames(List<String> subjectAlternativeNames) {
        this.subjectAlternativeNames = subjectAlternativeNames;
    }

    public List<String> getRelatedDomains() {
        return relatedDomains;
    }

    public void setRelatedDomains(List<String> relatedDomains) {
        this.relatedDomains = relatedDomains;
    }

    @Override
    public String toString() {
        return "CertificateInfo{" +
                "domain='" + domain + '\'' +
                ", issuer='" + issuer + '\'' +
                ", subject='" + subject + '\'' +
                ", sha256Fingerprint='" + sha256Fingerprint + '\'' +
                ", notBefore=" + notBefore +
                ", notAfter=" + notAfter +
                ", relatedDomainsCount=" + (relatedDomains != null ? relatedDomains.size() : 0) +
                '}';
    }
}
