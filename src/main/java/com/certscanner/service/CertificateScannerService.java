package com.certscanner.service;

import com.certscanner.model.CertificateInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * 证书扫描器主服务类
 */
public class CertificateScannerService {
    private static final Logger logger = LoggerFactory.getLogger(CertificateScannerService.class);
    
    private final CertificateService certificateService;
    private final CertificateSearchService searchService;
    private final ExecutorService executorService;

    public CertificateScannerService() {
        this.certificateService = new CertificateService();
        this.searchService = new CertificateSearchService();
        this.executorService = Executors.newFixedThreadPool(10);
    }

    /**
     * 扫描单个域名的证书信息和相关域名
     */
    public CertificateInfo scanDomain(String domain) {
        logger.info("开始扫描域名: {}", domain);
        
        try {
            // 获取证书信息
            CertificateInfo certInfo = certificateService.getCertificateInfo(domain);
            if (certInfo == null) {
                logger.warn("无法获取域名 {} 的证书信息", domain);
                return null;
            }

            // 查找使用相同证书的其他域名
            List<String> relatedDomains = searchService.findRelatedDomains(domain);
            certInfo.setRelatedDomains(relatedDomains);

            logger.info("域名 {} 扫描完成，找到 {} 个相关域名", domain, relatedDomains.size());
            return certInfo;

        } catch (Exception e) {
            logger.error("扫描域名 {} 时发生错误: {}", domain, e.getMessage());
            return null;
        }
    }

    /**
     * 批量扫描多个域名
     */
    public List<CertificateInfo> scanDomains(List<String> domains, ProgressCallback callback) {
        logger.info("开始批量扫描 {} 个域名", domains.size());
        
        List<CertificateInfo> results = new ArrayList<>();
        List<Future<CertificateInfo>> futures = new ArrayList<>();

        // 提交所有扫描任务
        for (String domain : domains) {
            Future<CertificateInfo> future = executorService.submit(() -> scanDomain(domain));
            futures.add(future);
        }

        // 收集结果
        int completed = 0;
        for (int i = 0; i < futures.size(); i++) {
            try {
                Future<CertificateInfo> future = futures.get(i);
                CertificateInfo result = future.get(30, TimeUnit.SECONDS); // 30秒超时
                
                if (result != null) {
                    results.add(result);
                }
                
                completed++;
                if (callback != null) {
                    callback.onProgress(completed, domains.size(), domains.get(i));
                }
                
            } catch (TimeoutException e) {
                logger.warn("扫描域名 {} 超时", domains.get(i));
                completed++;
                if (callback != null) {
                    callback.onProgress(completed, domains.size(), domains.get(i));
                }
            } catch (Exception e) {
                logger.error("处理域名 {} 的扫描结果时发生错误: {}", domains.get(i), e.getMessage());
                completed++;
                if (callback != null) {
                    callback.onProgress(completed, domains.size(), domains.get(i));
                }
            }
        }

        logger.info("批量扫描完成，成功扫描 {} 个域名", results.size());
        return results;
    }

    /**
     * 根据证书指纹查找相关域名
     */
    public List<String> findDomainsByFingerprint(String sha256Fingerprint) {
        logger.info("根据证书指纹查找相关域名: {}", sha256Fingerprint.substring(0, 16) + "...");
        
        try {
            return searchService.findDomainsByCertificateFingerprint(sha256Fingerprint);
        } catch (Exception e) {
            logger.error("根据指纹查找域名时发生错误: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 关闭服务，释放资源
     */
    public void shutdown() {
        logger.info("正在关闭证书扫描器服务...");
        
        try {
            executorService.shutdown();
            if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        searchService.close();
        logger.info("证书扫描器服务已关闭");
    }

    /**
     * 进度回调接口
     */
    public interface ProgressCallback {
        void onProgress(int completed, int total, String currentDomain);
    }
}
