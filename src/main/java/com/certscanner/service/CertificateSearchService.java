package com.certscanner.service;

import com.certscanner.model.CertificateInfo;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 证书搜索服务，用于查找使用相同证书的其他域名
 */
public class CertificateSearchService {
    private static final Logger logger = LoggerFactory.getLogger(CertificateSearchService.class);
    private static final String CRT_SH_API_BASE = "https://crt.sh";
    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;

    public CertificateSearchService() {
        this.httpClient = HttpClients.createDefault();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 根据证书指纹查找使用相同证书的域名
     */
    public List<String> findDomainsByCertificateFingerprint(String sha256Fingerprint) {
        Set<String> domains = new HashSet<>();
        
        try {
            // 使用crt.sh API查询
            List<String> crtShDomains = searchByCrtSh(sha256Fingerprint);
            domains.addAll(crtShDomains);
            
            logger.info("通过证书指纹 {} 找到 {} 个相关域名", 
                       sha256Fingerprint.substring(0, 16) + "...", domains.size());
            
        } catch (Exception e) {
            logger.error("搜索证书相关域名时发生错误: {}", e.getMessage());
        }
        
        return new ArrayList<>(domains);
    }

    /**
     * 根据域名查找使用相同证书的其他域名
     */
    public List<String> findRelatedDomains(String domain) {
        Set<String> relatedDomains = new HashSet<>();
        
        try {
            // 首先通过域名搜索证书
            String searchUrl = String.format("%s/?q=%s&output=json",
                                            CRT_SH_API_BASE,
                                            URLEncoder.encode(domain, "UTF-8"));
            
            String response = makeHttpRequest(searchUrl);
            if (response != null) {
                JsonNode certificates = objectMapper.readTree(response);
                
                // 获取所有证书的域名信息
                for (JsonNode cert : certificates) {
                    String commonName = cert.path("common_name").asText();
                    String nameValue = cert.path("name_value").asText();
                    
                    if (!commonName.isEmpty()) {
                        relatedDomains.add(commonName);
                    }
                    
                    // name_value可能包含多个域名，用换行符分隔
                    if (!nameValue.isEmpty()) {
                        String[] names = nameValue.split("\n");
                        for (String name : names) {
                            name = name.trim();
                            if (!name.isEmpty() && isValidDomain(name)) {
                                relatedDomains.add(name);
                            }
                        }
                    }
                }
            }
            
            // 移除原始域名
            relatedDomains.remove(domain);
            
            logger.info("为域名 {} 找到 {} 个相关域名", domain, relatedDomains.size());
            
        } catch (Exception e) {
            logger.error("搜索域名 {} 的相关域名时发生错误: {}", domain, e.getMessage());
        }
        
        return new ArrayList<>(relatedDomains);
    }

    /**
     * 使用crt.sh API根据SHA256指纹搜索
     */
    private List<String> searchByCrtSh(String sha256Fingerprint) throws IOException {
        Set<String> domains = new HashSet<>();
        
        // 移除指纹中的冒号
        String cleanFingerprint = sha256Fingerprint.replace(":", "").toLowerCase();
        
        String searchUrl = String.format("%s/?sha256=%s&output=json", 
                                        CRT_SH_API_BASE, cleanFingerprint);
        
        String response = makeHttpRequest(searchUrl);
        if (response != null) {
            JsonNode certificates = objectMapper.readTree(response);
            
            for (JsonNode cert : certificates) {
                String commonName = cert.path("common_name").asText();
                String nameValue = cert.path("name_value").asText();
                
                if (!commonName.isEmpty()) {
                    domains.add(commonName);
                }
                
                if (!nameValue.isEmpty()) {
                    String[] names = nameValue.split("\n");
                    for (String name : names) {
                        name = name.trim();
                        if (!name.isEmpty() && isValidDomain(name)) {
                            domains.add(name);
                        }
                    }
                }
            }
        }
        
        return new ArrayList<>(domains);
    }

    /**
     * 发送HTTP请求
     */
    private String makeHttpRequest(String url) {
        try {
            HttpGet request = new HttpGet(url);
            request.setHeader("User-Agent", "Certificate-Scanner/1.0");
            
            HttpResponse response = httpClient.execute(request);
            int statusCode = response.getStatusLine().getStatusCode();
            
            if (statusCode == 200) {
                return EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            } else {
                logger.warn("HTTP请求失败，状态码: {}, URL: {}", statusCode, url);
            }
            
        } catch (Exception e) {
            logger.error("HTTP请求异常: {}", e.getMessage());
        }
        
        return null;
    }

    /**
     * 验证域名格式是否有效
     */
    private boolean isValidDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }
        
        // 简单的域名格式验证
        domain = domain.trim().toLowerCase();
        
        // 检查是否包含无效字符
        if (domain.contains(" ") || domain.contains("\t") || domain.contains("\n")) {
            return false;
        }
        
        // 检查是否以通配符开头
        if (domain.startsWith("*.")) {
            domain = domain.substring(2);
        }
        
        // 基本的域名格式检查
        return domain.matches("^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?)*$");
    }

    /**
     * 关闭HTTP客户端资源
     */
    public void close() {
        try {
            if (httpClient instanceof AutoCloseable) {
                ((AutoCloseable) httpClient).close();
            }
        } catch (Exception e) {
            logger.warn("关闭HTTP客户端时发生错误: {}", e.getMessage());
        }
    }
}
