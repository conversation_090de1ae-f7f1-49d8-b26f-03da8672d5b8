package com.certscanner.service;

import com.certscanner.model.CertificateInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.IOException;
import java.net.Socket;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * 证书服务类，负责获取和解析SSL证书
 */
public class CertificateService {
    private static final Logger logger = LoggerFactory.getLogger(CertificateService.class);
    private static final int DEFAULT_HTTPS_PORT = 443;
    private static final int CONNECTION_TIMEOUT = 10000; // 10 seconds

    /**
     * 获取域名的SSL证书信息
     */
    public CertificateInfo getCertificateInfo(String domain) {
        try {
            X509Certificate certificate = getCertificate(domain, DEFAULT_HTTPS_PORT);
            if (certificate == null) {
                logger.warn("无法获取域名 {} 的证书", domain);
                return null;
            }

            CertificateInfo certInfo = new CertificateInfo(domain);
            populateCertificateInfo(certInfo, certificate);
            return certInfo;

        } catch (Exception e) {
            logger.error("获取域名 {} 的证书时发生错误: {}", domain, e.getMessage());
            return null;
        }
    }

    /**
     * 从指定域名和端口获取SSL证书
     */
    private X509Certificate getCertificate(String hostname, int port) throws IOException {
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };

            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

            SSLSocketFactory factory = sslContext.getSocketFactory();
            
            try (SSLSocket socket = (SSLSocket) factory.createSocket()) {
                socket.connect(new java.net.InetSocketAddress(hostname, port), CONNECTION_TIMEOUT);
                socket.startHandshake();
                
                Certificate[] certificates = socket.getSession().getPeerCertificates();
                if (certificates.length > 0 && certificates[0] instanceof X509Certificate) {
                    return (X509Certificate) certificates[0];
                }
            }
        } catch (Exception e) {
            logger.error("连接到 {}:{} 时发生错误: {}", hostname, port, e.getMessage());
            throw new IOException("无法获取证书: " + e.getMessage(), e);
        }
        
        return null;
    }

    /**
     * 填充证书信息
     */
    private void populateCertificateInfo(CertificateInfo certInfo, X509Certificate certificate) {
        try {
            // 基本信息
            certInfo.setIssuer(certificate.getIssuerDN().getName());
            certInfo.setSubject(certificate.getSubjectDN().getName());
            certInfo.setSerialNumber(certificate.getSerialNumber().toString(16).toUpperCase());

            // 有效期
            certInfo.setNotBefore(LocalDateTime.ofInstant(
                certificate.getNotBefore().toInstant(), ZoneId.systemDefault()));
            certInfo.setNotAfter(LocalDateTime.ofInstant(
                certificate.getNotAfter().toInstant(), ZoneId.systemDefault()));

            // 计算指纹
            certInfo.setSha1Fingerprint(calculateFingerprint(certificate, "SHA-1"));
            certInfo.setSha256Fingerprint(calculateFingerprint(certificate, "SHA-256"));

            // 获取SAN (Subject Alternative Names)
            List<String> sanList = extractSubjectAlternativeNames(certificate);
            certInfo.setSubjectAlternativeNames(sanList);

        } catch (Exception e) {
            logger.error("解析证书信息时发生错误: {}", e.getMessage());
        }
    }

    /**
     * 计算证书指纹
     */
    private String calculateFingerprint(X509Certificate certificate, String algorithm) {
        try {
            MessageDigest md = MessageDigest.getInstance(algorithm);
            byte[] digest = md.digest(certificate.getEncoded());
            
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02X:", b & 0xFF));
            }
            
            // 移除最后一个冒号
            if (sb.length() > 0) {
                sb.setLength(sb.length() - 1);
            }
            
            return sb.toString();
        } catch (Exception e) {
            logger.error("计算 {} 指纹时发生错误: {}", algorithm, e.getMessage());
            return null;
        }
    }

    /**
     * 提取证书的Subject Alternative Names
     */
    private List<String> extractSubjectAlternativeNames(X509Certificate certificate) {
        List<String> sanList = new ArrayList<>();
        
        try {
            Collection<List<?>> sans = certificate.getSubjectAlternativeNames();
            if (sans != null) {
                for (List<?> san : sans) {
                    if (san.size() >= 2) {
                        Integer type = (Integer) san.get(0);
                        String value = (String) san.get(1);
                        
                        // Type 2 = DNS Name
                        if (type == 2) {
                            sanList.add(value);
                        }
                    }
                }
            }
        } catch (CertificateException e) {
            logger.warn("提取SAN时发生错误: {}", e.getMessage());
        }
        
        return sanList;
    }
}
