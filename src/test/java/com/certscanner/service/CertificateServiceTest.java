package com.certscanner.service;

import com.certscanner.model.CertificateInfo;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 证书服务测试类
 */
public class CertificateServiceTest {

    @Test
    public void testGetCertificateInfo() {
        CertificateService service = new CertificateService();
        
        // 测试获取Google的证书信息
        CertificateInfo certInfo = service.getCertificateInfo("www.google.com");
        
        assertNotNull("证书信息不应为空", certInfo);
        assertEquals("域名应该匹配", "www.google.com", certInfo.getDomain());
        assertNotNull("颁发者不应为空", certInfo.getIssuer());
        assertNotNull("主题不应为空", certInfo.getSubject());
        assertNotNull("SHA256指纹不应为空", certInfo.getSha256Fingerprint());
        assertNotNull("有效期开始时间不应为空", certInfo.getNotBefore());
        assertNotNull("有效期结束时间不应为空", certInfo.getNotAfter());
        
        System.out.println("证书信息: " + certInfo);
    }

    @Test
    public void testInvalidDomain() {
        CertificateService service = new CertificateService();
        
        // 测试无效域名
        CertificateInfo certInfo = service.getCertificateInfo("invalid-domain-that-does-not-exist.com");
        
        // 对于无效域名，应该返回null或者抛出异常
        // 这里我们期望返回null
        assertNull("无效域名应该返回null", certInfo);
    }
}
