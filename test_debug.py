#!/usr/bin/env python3.10

print("开始测试...")

try:
    from censys_selenium_scanner_fixed import CensysSeleniumScanner
    print("导入成功")
    
    scanner = CensysSeleniumScanner(
        cookie="auth_v1=MTc1NDE4MTM1NXxNbDZfSGFDcVVNRkZpWTdhRU44T09aemNQaDRGY0RCbTV1NEpnekpUNWdUbFAzSENGRjlqX292RGtGV1JUUl9rQ0Zfc3ZIYjR5VEtjS1lIWHRsUS1LSVFpMXUwblI1VjZqdGowNXhuTWVVY2I0UWszZWI3Y0NsWjJzcTl5RHZFa0cwOUdDMEJZQ1NMeS16MTRiSHlMRzJoQ3R2cTNoNlRPNEVoajhtWVFOSUphMDU1dVJ3V1M5S1VPTlExY1FRZHB1MUtrYVFDQTVYWXRKYnY5cnItWEdVNHN1Ym9rR0QzVlZVcmFLSWc4YU9MY24tdFN4TE4xRUw1NE5ZS3hvM085OFpMbl9fMzRvZjFrR0hzcUhYVT18jvB7u9Nx34pevFb9vruVpKcSQRgINZSUiUhaqtwpp9o=",
        verbose=True,
        headless=True
    )
    print("扫描器创建成功")
    
    print("开始搜索...")
    result = scanner.search_censys("0669d322c42fda981eb015b42ec64682c68d47b6354ae536ec12aa6b5a299058")
    print(f"搜索完成，结果: {result}")
    
except Exception as e:
    print(f"发生错误: {e}")
    import traceback
    traceback.print_exc()
